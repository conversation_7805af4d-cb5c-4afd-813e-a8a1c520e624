import { Inject, Singleton } from '../../core/ioc';
import Logger, { LogClass } from '../../core/logging';
import { ActiveSweepstake } from '../sweepstake-active';
import { NFTRequirementValidator, NFTHolderRequirement } from '../../nft/validation';

export interface SweepstakeGameBuyInNFTRequirementResult {
    passed: boolean;
}

@Singleton
@LogClass()
export class SweepstakeGameBuyInNFTRequirementProcessor {
    constructor(
        @Inject private readonly nftRequirementValidator: NFTRequirementValidator) {
    }

    /**
     * Processes and validates NFT holder requirements for a sweepstake game buy-in.
     *
     * @param sweepstake The active sweepstake to check requirements for
     * @param userId The user ID to validate
     * @returns A result object indicating whether the user passed the NFT requirement check
     */
    public async process(sweepstake: ActiveSweepstake, userId: number): Promise<SweepstakeGameBuyInNFTRequirementResult> {
        const sweepstakeId = sweepstake.id;
        const requirements = this.extractRequirements(sweepstake);

        // If no requirements exist, user passes by default
        if (requirements.length === 0) {
            Logger.debug('No NFT holder requirements to check. Passing by default.', { userId, sweepstakeId });
            return { passed: true };
        }

        Logger.debug('Checking if user owns any NFTs from required collections.', {
            userId,
            sweepstakeId,
            requirementCount: requirements.length
        });

        const ownsAnyNFT = await this.nftRequirementValidator.checkAnyNFTOwnership(userId, requirements);

        Logger.debug('NFT ownership check result.', { userId, sweepstakeId, ownsAnyNFT });

        if (!ownsAnyNFT) {
            Logger.info('User does not meet NFT requirements for sweepstake.', { userId, sweepstakeId });
            return { passed: false };
        }

        return { passed: true };
    }

    private extractRequirements(sweepstake: ActiveSweepstake): NFTHolderRequirement[] {
        const nftHolderRequirements: NFTHolderRequirement[] = [];
        const requirements = sweepstake.metadata?.nftHolderRequirements;

        if (!Array.isArray(requirements) || requirements.length === 0)
            return nftHolderRequirements;

        Logger.debug('NFT requirements found in metadata.', {
            sweepstakeId: sweepstake.id,
            count: requirements.length
        });

        for (const req of requirements)
            nftHolderRequirements.push({
                contractAddress: req.contractAddress,
                network: req.network
            });

        return nftHolderRequirements;
    }
}

import { Inject, Singleton } from '../../core/ioc';
import Logger, { LogClass } from '../../core/logging';
import { UserManager, UserAliasType } from '../../user';
import { NFTManager } from '../nft-manager';
import { BlockchainNetwork } from '../../blockchain';

export interface NFTHolderRequirement {
    contractAddress: string;
    network?: BlockchainNetwork;
}

@Singleton
@LogClass()
export class NFTRequirementValidator {
    constructor(
        @Inject private readonly userManager: UserManager,
        @Inject private readonly nftManager: NFTManager) {
    }

    /**
     * Checks if a user owns at least one NFT from a specific collection
     * @param userId The user ID to check
     * @param requirement The NFT requirement to check against
     * @returns True if the user owns at least one NFT from the collection, false otherwise
     */
    public async checkNFTOwnership(userId: number, requirement: NFTHolderRequirement): Promise<boolean> {
        try {
            const user = await this.userManager.get(userId);

            if (!user) {
                Logger.warn(`User ${userId} not found. Cannot check NFT ownership.`);
                return false;
            }

            const ethereumAddress = user.aliases.find(a => a.type === UserAliasType.EthereumAddress)?.value;

            if (!ethereumAddress) {
                Logger.warn(`User ${userId} does not have an Ethereum address. Cannot check NFT ownership.`);
                return false;
            }

            const result = await this.nftManager.getOwnerTokens(ethereumAddress, requirement.contractAddress, requirement.network || BlockchainNetwork.Ethereum);

            return result.length > 0;
        } catch (error) {
            Logger.error(`Failed to check NFT ownership for user ${userId}`, error);
            return false;
        }
    }

    /**
     * Checks if a user owns at least one NFT from any of the specified collections
     * @param userId The user ID to check
     * @param requirements The NFT requirements to check against
     * @returns True if the user owns at least one NFT from any of the collections, false otherwise
     */
    public async checkAnyNFTOwnership(userId: number, requirements: NFTHolderRequirement[]): Promise<boolean> {
        if (!requirements.length)
            return true;

        for (const requirement of requirements) {
            Logger.debug(`Checking NFT ownership for user ${userId} in collection ${requirement.contractAddress} on ${requirement.network || BlockchainNetwork.Ethereum}...`);

            if (await this.checkNFTOwnership(userId, requirement)) {
                Logger.debug(`User ${userId} owns at least one NFT from collection ${requirement.contractAddress}. Requirement satisfied.`);
                return true;
            }
        }

        Logger.info(`User ${userId} does not own any NFTs from the required collections.`);

        return false;
    }
}
